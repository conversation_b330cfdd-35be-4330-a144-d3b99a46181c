/**
 * SignalEngine IPC Interfaces
 * TypeScript interfaces for SignalEngine IPC communication between renderer and main process
 * Ensures type safety and proper error handling for all SignalEngine operations
 */

import type {
  Strategy,
  GeneratedSignal,
  SignalEnginePerformanceMetrics,
  StrategyConfig
} from '../../../shared/types/signals'

/**
 * Generic IPC response interface for SignalEngine operations
 * @template T - Type of the data payload
 */
export interface SignalEngineIPCResponse<T = unknown> {
  /** Whether the operation was successful */
  success: boolean
  /** Success message (optional) */
  message?: string
  /** Error message if operation failed */
  error?: string
  /** Response data payload */
  data?: T
}

/**
 * IPC channel names for SignalEngine operations
 */
export const SIGNAL_ENGINE_IPC_CHANNELS = {
  /** Get active strategies */
  GET_ACTIVE_STRATEGIES: 'signal-engine:get-active-strategies',
  /** Get signal history (recent signals) */
  GET_SIGNAL_HISTORY: 'signal-engine:get-signal-history',
  /** Get performance metrics */
  GET_PERFORMANCE_METRICS: 'signal-engine:get-performance-metrics',
  /** Create new strategy */
  CREATE_STRATEGY: 'signal-engine:create-strategy',
  /** Remove existing strategy */
  REMOVE_STRATEGY: 'signal-engine:remove-strategy',
  /** Initialize SignalEngine */
  INITIALIZE: 'signal-engine:initialize',
  /** Check if SignalEngine is initialized */
  IS_INITIALIZED: 'signal-engine:is-initialized'
} as const

/**
 * Response type for getting active strategies
 */
export interface GetActiveStrategiesResponse extends SignalEngineIPCResponse<Strategy[]> {}

/**
 * Response type for getting signal history
 */
export interface GetSignalHistoryResponse extends SignalEngineIPCResponse<GeneratedSignal[]> {}

/**
 * Response type for getting performance metrics
 */
export interface GetPerformanceMetricsResponse extends SignalEngineIPCResponse<SignalEnginePerformanceMetrics> {}

/**
 * Request parameters for creating a strategy
 */
export interface CreateStrategyRequest {
  /** Single indicator name or array of indicator names */
  indicators: string | string[]
  /** Strategy configuration */
  config: Record<string, unknown>
}

/**
 * Response type for creating a strategy
 */
export interface CreateStrategyResponse extends SignalEngineIPCResponse<Strategy> {}

/**
 * Request parameters for removing a strategy
 */
export interface RemoveStrategyRequest {
  /** Name of the strategy to remove */
  strategyName: string
}

/**
 * Response type for removing a strategy
 */
export interface RemoveStrategyResponse extends SignalEngineIPCResponse<{ removed: boolean }> {}

/**
 * Response type for initialization operations
 */
export interface InitializationResponse extends SignalEngineIPCResponse<void> {}

/**
 * Response type for checking initialization status
 */
export interface IsInitializedResponse extends SignalEngineIPCResponse<{ isInitialized: boolean }> {}

/**
 * Request parameters for getting signal history
 */
export interface GetSignalHistoryRequest {
  /** Maximum number of signals to return (optional) */
  maxSignals?: number
  /** Strategy name filter (optional) */
  strategyName?: string
}

/**
 * SignalEngine event data types for WebSocket communication
 */
export interface SignalEngineEventData {
  'signal-generated': {
    signal: GeneratedSignal
    timestamp: number
    metadata?: Record<string, unknown>
  }
  'strategy-created': {
    strategy: Strategy
    timestamp: number
  }
  'strategy-updated': {
    strategy: Strategy
    changes: Partial<Strategy>
    timestamp: number
  }
  'strategy-deleted': {
    strategyId: string
    timestamp: number
  }
  'performance-update': {
    metrics: SignalEnginePerformanceMetrics
    timestamp: number
  }
  'connection-status': {
    status: 'connected' | 'disconnected' | 'reconnecting' | 'error'
    timestamp: number
    details?: string
  }
  'error': {
    error: string
    code?: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    timestamp: number
    context?: Record<string, unknown>
  }
  'heartbeat': {
    timestamp: number
    health: 'healthy' | 'warning' | 'critical'
    metrics?: {
      signalCount: number
      activeStrategies: number
      processingTime: number
    }
  }
}

/**
 * Event handler configuration for SignalEngine events
 */
export interface SignalEngineEventHandlers {
  /** Called when a new signal is generated */
  onSignalGenerated?: (data: SignalEngineEventData['signal-generated']) => void
  /** Called when a strategy is created */
  onStrategyCreated?: (data: SignalEngineEventData['strategy-created']) => void
  /** Called when a strategy is updated */
  onStrategyUpdated?: (data: SignalEngineEventData['strategy-updated']) => void
  /** Called when a strategy is deleted */
  onStrategyDeleted?: (data: SignalEngineEventData['strategy-deleted']) => void
  /** Called when performance metrics are updated */
  onPerformanceUpdate?: (data: SignalEngineEventData['performance-update']) => void
  /** Called when connection status changes */
  onConnectionStatus?: (data: SignalEngineEventData['connection-status']) => void
  /** Called when an error occurs */
  onError?: (data: SignalEngineEventData['error']) => void
  /** Called on heartbeat events */
  onHeartbeat?: (data: SignalEngineEventData['heartbeat']) => void
}

/**
 * Configuration for SignalEngine service
 */
export interface SignalEngineServiceConfig {
  /** Whether to enable automatic initialization */
  autoInitialize?: boolean
  /** Whether to enable event listeners */
  enableEvents?: boolean
  /** Timeout for IPC operations in milliseconds */
  ipcTimeout?: number
  /** Whether to enable debug logging */
  enableDebugLogging?: boolean
}

/**
 * Default configuration for SignalEngine service
 */
export const DEFAULT_SIGNAL_ENGINE_CONFIG: Required<SignalEngineServiceConfig> = {
  autoInitialize: true,
  enableEvents: true,
  ipcTimeout: 10000, // 10 seconds
  enableDebugLogging: false
} as const

/**
 * Type guard to check if response is a valid SignalEngine IPC response
 */
export function isSignalEngineIPCResponse<T>(obj: unknown): obj is SignalEngineIPCResponse<T> {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'success' in obj &&
    typeof (obj as any).success === 'boolean'
  )
}

/**
 * Type guard to check if response contains data
 */
export function hasResponseData<T>(response: SignalEngineIPCResponse<T>): response is SignalEngineIPCResponse<T> & { data: T } {
  return response.success && response.data !== undefined
}

/**
 * Cleanup function type for event listeners
 */
export type SignalEngineEventCleanup = () => void

/**
 * Event listener configuration
 */
export interface SignalEngineEventConfig<T = any> {
  /** Event name to listen for */
  eventName: keyof SignalEngineEventData
  /** Event handler function */
  handler: (data: T) => void
  /** Whether the listener is enabled */
  enabled?: boolean
}
