/**
 * Signal Engine Event Emitter
 * Comprehensive WebSocket event-driven architecture for real-time Signal Engine updates
 */

import { EventEmitter } from 'events'
import { <PERSON>rowserWindow } from 'electron'
import { logger } from '../../shared/utils/logger'
import { WEBSOCKET_CONSTANTS } from '../../shared/constants'
import type {
  GeneratedSignal,
  Strategy,
  SignalEnginePerformanceMetrics
} from '../../shared/types/signals'

/**
 * Signal Engine event data types
 */
export interface SignalEngineEventData {
  'signal-generated': {
    signal: GeneratedSignal
    timestamp: number
    metadata?: Record<string, unknown>
  }
  'strategy-created': {
    strategy: Strategy
    timestamp: number
  }
  'strategy-updated': {
    strategy: Strategy
    changes: Partial<Strategy>
    timestamp: number
  }
  'strategy-deleted': {
    strategyId: string
    timestamp: number
  }
  'performance-update': {
    metrics: SignalEnginePerformanceMetrics
    timestamp: number
  }
  'connection-status': {
    status: 'connected' | 'disconnected' | 'reconnecting' | 'error'
    timestamp: number
    details?: string
  }
  error: {
    error: string
    code?: string
    severity: 'low' | 'medium' | 'high' | 'critical'
    timestamp: number
    context?: Record<string, unknown>
  }
  heartbeat: {
    timestamp: number
    health: 'healthy' | 'degraded' | 'unhealthy'
    metrics?: {
      signalCount: number
      activeStrategies: number
      processingTime: number
    }
  }
}

/**
 * Event listener configuration
 */
export interface EventListenerConfig {
  once?: boolean
  timeout?: number
  filter?: (data: unknown) => boolean
}

/**
 * Signal Engine Event Emitter Class
 * Provides comprehensive event-driven architecture for Signal Engine
 */
export class SignalEngineEventEmitter extends EventEmitter {
  private static instance: SignalEngineEventEmitter | null = null
  private windows: Set<BrowserWindow> = new Set()
  private eventHistory: Array<{ event: string; data: unknown; timestamp: number }> = []
  private maxHistorySize = 1000
  private isEnabled = true

  /**
   * Get singleton instance
   */
  public static getInstance(): SignalEngineEventEmitter {
    if (!SignalEngineEventEmitter.instance) {
      SignalEngineEventEmitter.instance = new SignalEngineEventEmitter()
    }
    return SignalEngineEventEmitter.instance
  }

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    super()
    this.setMaxListeners(50) // Increase max listeners for multiple components
    this.setupInternalListeners()
  }

  /**
   * Register a browser window for event broadcasting
   */
  public registerWindow(window: BrowserWindow): void {
    this.windows.add(window)

    // Clean up when window is closed
    window.on('closed', () => {
      this.windows.delete(window)
    })

    logger.debug('SignalEngineEvents', `Registered window, total: ${this.windows.size}`)
  }

  /**
   * Unregister a browser window
   */
  public unregisterWindow(window: BrowserWindow): void {
    this.windows.delete(window)
    logger.debug('SignalEngineEvents', `Unregistered window, total: ${this.windows.size}`)
  }

  /**
   * Emit a Signal Engine event
   */
  public emitSignalEngineEvent<K extends keyof SignalEngineEventData>(
    event: K,
    data: SignalEngineEventData[K]
  ): void {
    if (!this.isEnabled) {
      return
    }

    try {
      // Add to event history
      this.addToHistory(event, data)

      // Emit to internal listeners
      this.emit(event, data)

      // Broadcast to all registered windows
      this.broadcastToWindows(event, data)

      logger.debug('SignalEngineEvents', `Emitted event: ${event}`, {
        windowCount: this.windows.size,
        dataSize: JSON.stringify(data).length
      })
    } catch (error) {
      logger.error('SignalEngineEvents', `Failed to emit event ${event}:`, error)
    }
  }

  /**
   * Add event listener with enhanced configuration
   */
  public addListener<K extends keyof SignalEngineEventData>(
    event: K,
    listener: (data: SignalEngineEventData[K]) => void,
    config: EventListenerConfig = {}
  ): () => void {
    const { once = false, timeout, filter } = config

    // Wrap listener with filter and timeout logic
    const wrappedListener = (data: SignalEngineEventData[K]): void => {
      try {
        // Apply filter if provided
        if (filter && !filter(data)) {
          return
        }

        // Call the actual listener
        listener(data)
      } catch (error) {
        logger.error('SignalEngineEvents', `Error in event listener for ${event}:`, error)
      }
    }

    // Add listener
    if (once) {
      this.once(event, wrappedListener)
    } else {
      this.on(event, wrappedListener)
    }

    // Set up timeout if provided
    let timeoutId: NodeJS.Timeout | undefined
    if (timeout) {
      timeoutId = setTimeout(() => {
        this.removeListener(event, wrappedListener)
        logger.debug('SignalEngineEvents', `Removed listener for ${event} due to timeout`)
      }, timeout)
    }

    // Return cleanup function
    return () => {
      this.removeListener(event, wrappedListener)
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }

  /**
   * Get event history
   */
  public getEventHistory(
    eventType?: keyof SignalEngineEventData,
    limit?: number
  ): Array<{ event: string; data: unknown; timestamp: number }> {
    let history = this.eventHistory

    if (eventType) {
      history = history.filter((entry) => entry.event === eventType)
    }

    if (limit) {
      history = history.slice(-limit)
    }

    return [...history] // Return copy
  }

  /**
   * Clear event history
   */
  public clearEventHistory(): void {
    this.eventHistory = []
    logger.debug('SignalEngineEvents', 'Event history cleared')
  }

  /**
   * Enable/disable event emission
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    logger.info('SignalEngineEvents', `Event emission ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Get current status
   */
  public getStatus(): {
    enabled: boolean
    registeredWindows: number
    eventHistorySize: number
    listenerCount: Record<string, number>
  } {
    const listenerCount: Record<string, number> = {}

    for (const event of Object.keys(WEBSOCKET_CONSTANTS.EVENTS)) {
      listenerCount[event] = this.listenerCount(event)
    }

    return {
      enabled: this.isEnabled,
      registeredWindows: this.windows.size,
      eventHistorySize: this.eventHistory.length,
      listenerCount
    }
  }

  /**
   * Broadcast heartbeat with Signal Engine metrics
   */
  public broadcastHeartbeat(metrics?: {
    signalCount: number
    activeStrategies: number
    processingTime: number
  }): void {
    this.emitSignalEngineEvent('heartbeat', {
      timestamp: Date.now(),
      health: this.determineHealth(metrics),
      metrics
    })
  }

  /**
   * Setup internal event listeners for logging and monitoring
   */
  private setupInternalListeners(): void {
    // Log all events for debugging
    this.on('signal-generated', (data) => {
      logger.info(
        'SignalEngineEvents',
        `Signal generated: ${data.signal.signal} (${(data.signal.confidence * 100).toFixed(1)}%)`
      )
    })

    this.on('strategy-created', (data) => {
      logger.info('SignalEngineEvents', `Strategy created: ${data.strategy.name}`)
    })

    this.on('strategy-deleted', (data) => {
      logger.info('SignalEngineEvents', `Strategy deleted: ${data.strategyId}`)
    })

    this.on('error', (data) => {
      logger.error('SignalEngineEvents', `Signal Engine error: ${data.error}`, data.context)
    })
  }

  /**
   * Add event to history with size management
   */
  private addToHistory(event: string, data: unknown): void {
    this.eventHistory.push({
      event,
      data: JSON.parse(JSON.stringify(data)), // Deep clone
      timestamp: Date.now()
    })

    // Trim history if it exceeds max size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize)
    }
  }

  /**
   * Broadcast event to all registered windows
   */
  private broadcastToWindows(event: string, data: unknown): void {
    const payload = {
      type: event,
      data,
      timestamp: Date.now()
    }

    for (const window of this.windows) {
      try {
        if (!window.isDestroyed()) {
          window.webContents.send('signal-engine-event', payload)
        }
      } catch (error) {
        logger.error('SignalEngineEvents', `Failed to send event to window:`, error)
        // Remove destroyed window
        this.windows.delete(window)
      }
    }
  }

  /**
   * Determine system health based on metrics
   */
  private determineHealth(metrics?: {
    signalCount: number
    activeStrategies: number
    processingTime: number
  }): 'healthy' | 'degraded' | 'unhealthy' {
    if (!metrics) {
      return 'healthy'
    }

    const { processingTime, activeStrategies } = metrics

    if (processingTime > 1000 || activeStrategies === 0) {
      return 'unhealthy'
    } else if (processingTime > 500) {
      return 'degraded'
    } else {
      return 'healthy'
    }
  }

  /**
   * Reset singleton instance (for testing)
   */
  public static resetInstance(): void {
    if (SignalEngineEventEmitter.instance) {
      SignalEngineEventEmitter.instance.removeAllListeners()
      SignalEngineEventEmitter.instance = null
    }
  }
}

/**
 * Convenience functions for common events
 */
export const SignalEngineEvents = {
  /**
   * Emit signal generated event
   */
  signalGenerated: (signal: GeneratedSignal, metadata?: Record<string, unknown>) => {
    SignalEngineEventEmitter.getInstance().emitSignalEngineEvent('signal-generated', {
      signal,
      timestamp: Date.now(),
      metadata
    })
  },

  /**
   * Emit strategy created event
   */
  strategyCreated: (strategy: Strategy) => {
    SignalEngineEventEmitter.getInstance().emitSignalEngineEvent('strategy-created', {
      strategy,
      timestamp: Date.now()
    })
  },

  /**
   * Emit strategy updated event
   */
  strategyUpdated: (strategy: Strategy, changes: Partial<Strategy>) => {
    SignalEngineEventEmitter.getInstance().emitSignalEngineEvent('strategy-updated', {
      strategy,
      changes,
      timestamp: Date.now()
    })
  },

  /**
   * Emit strategy deleted event
   */
  strategyDeleted: (strategyId: string) => {
    SignalEngineEventEmitter.getInstance().emitSignalEngineEvent('strategy-deleted', {
      strategyId,
      timestamp: Date.now()
    })
  },

  /**
   * Emit performance update event
   */
  performanceUpdate: (metrics: SignalEnginePerformanceMetrics) => {
    SignalEngineEventEmitter.getInstance().emitSignalEngineEvent('performance-update', {
      metrics,
      timestamp: Date.now()
    })
  },

  /**
   * Emit connection status event
   */
  connectionStatus: (
    status: 'connected' | 'disconnected' | 'reconnecting' | 'error',
    details?: string
  ) => {
    SignalEngineEventEmitter.getInstance().emitSignalEngineEvent('connection-status', {
      status,
      timestamp: Date.now(),
      details
    })
  },

  /**
   * Emit error event
   */
  error: (
    error: string,
    code?: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    context?: Record<string, unknown>
  ) => {
    SignalEngineEventEmitter.getInstance().emitSignalEngineEvent('error', {
      error,
      code,
      severity,
      timestamp: Date.now(),
      context
    })
  }
}

export default SignalEngineEventEmitter
