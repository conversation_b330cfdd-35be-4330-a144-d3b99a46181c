/**
 * SignalEngine Service for Renderer Process
 * Provides SignalEngine operations through IPC communication with the main process
 * Replaces direct imports from main process to maintain proper Electron architecture
 *
 * @example
 * ```typescript
 * // Get singleton instance
 * const signalEngine = SignalEngineService.getInstance()
 *
 * // Set up event listeners
 * const cleanup = signalEngine.setupEventListeners({
 *   onSignalGenerated: (data) => console.log('Signal:', data.signal),
 *   onPerformanceUpdate: (data) => console.log('Metrics:', data.metrics)
 * })
 *
 * // Get active strategies
 * const strategies = await signalEngine.getActiveStrategies()
 *
 * // Create a strategy
 * const strategy = await signalEngine.createStrategy('rsi', { period: 14 })
 * ```
 */

import type {
  Strategy,
  GeneratedSignal,
  SignalEnginePerformanceMetrics
} from '../../../shared/types/signals'
import type {
  SignalEngineIPCResponse,
  SignalEngineEventHandlers,
  SignalEngineServiceConfig,
  SignalEngineEventCleanup,
  GetActiveStrategiesResponse,
  GetSignalHistoryResponse,
  GetPerformanceMetricsResponse,
  CreateStrategyResponse,
  RemoveStrategyResponse,
  InitializationResponse,
  IsInitializedResponse,
  SIGNAL_ENGINE_IPC_CHANNELS,
  DEFAULT_SIGNAL_ENGINE_CONFIG,
  isSignalEngineIPCResponse,
  hasResponseData
} from '../interfaces/signalEngine'
import { logger } from '../../../shared/utils/logger'

/**
 * Logger category for SignalEngine service
 */
const LOG_CATEGORY = 'SignalEngineService' as const

/**
 * SignalEngine Service Class
 * Singleton service that provides SignalEngine operations through IPC communication
 * Maintains proper Electron process separation while providing the same API
 */
export class SignalEngineService {
  private static instance: SignalEngineService | null = null
  private readonly config: Required<SignalEngineServiceConfig>
  private eventListeners: Map<string, () => void> = new Map()
  private isInitialized: boolean = false

  /**
   * Private constructor for singleton pattern
   */
  private constructor(config?: Partial<SignalEngineServiceConfig>) {
    this.config = { ...DEFAULT_SIGNAL_ENGINE_CONFIG, ...config }

    if (this.config.enableDebugLogging) {
      logger.debug(LOG_CATEGORY, 'SignalEngineService instance created', { config: this.config })
    }
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<SignalEngineServiceConfig>): SignalEngineService {
    if (!SignalEngineService.instance) {
      SignalEngineService.instance = new SignalEngineService(config)
    }
    return SignalEngineService.instance
  }

  /**
   * Reset singleton instance (useful for testing)
   */
  public static resetInstance(): void {
    if (SignalEngineService.instance) {
      SignalEngineService.instance.dispose()
      SignalEngineService.instance = null
    }
  }

  /**
   * Initialize the SignalEngine service
   */
  public async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        logger.debug(LOG_CATEGORY, 'SignalEngineService already initialized')
        return
      }

      // Initialize SignalEngine in main process
      const response = await this.invokeWithTimeout<InitializationResponse>(
        SIGNAL_ENGINE_IPC_CHANNELS.INITIALIZE
      )

      if (!response.success) {
        throw new Error(response.error || 'Failed to initialize SignalEngine')
      }

      this.isInitialized = true
      logger.success(LOG_CATEGORY, 'SignalEngineService initialized successfully')
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Failed to initialize SignalEngineService:', error)
      throw error
    }
  }

  /**
   * Check if SignalEngine is initialized
   */
  public async getInitializationStatus(): Promise<boolean> {
    try {
      const response = await this.invokeWithTimeout<IsInitializedResponse>(
        SIGNAL_ENGINE_IPC_CHANNELS.IS_INITIALIZED
      )

      if (!response.success || !hasResponseData(response)) {
        return false
      }

      return response.data.isInitialized
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Failed to get initialization status:', error)
      return false
    }
  }

  /**
   * Get active strategies
   */
  public async getActiveStrategies(): Promise<Strategy[]> {
    try {
      const response = await this.invokeWithTimeout<GetActiveStrategiesResponse>(
        SIGNAL_ENGINE_IPC_CHANNELS.GET_ACTIVE_STRATEGIES
      )

      if (!response.success) {
        throw new Error(response.error || 'Failed to get active strategies')
      }

      return response.data || []
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Failed to get active strategies:', error)
      throw error
    }
  }

  /**
   * Get recent signals (signal history)
   */
  public async getRecentSignals(maxSignals?: number): Promise<GeneratedSignal[]> {
    try {
      const response = await this.invokeWithTimeout<GetSignalHistoryResponse>(
        SIGNAL_ENGINE_IPC_CHANNELS.GET_SIGNAL_HISTORY,
        maxSignals
      )

      if (!response.success) {
        throw new Error(response.error || 'Failed to get recent signals')
      }

      return response.data || []
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Failed to get recent signals:', error)
      throw error
    }
  }

  /**
   * Get signal history (alias for getRecentSignals for compatibility)
   */
  public async getSignalHistory(strategyName?: string): Promise<GeneratedSignal[]> {
    return this.getRecentSignals()
  }

  /**
   * Get performance metrics
   */
  public async getPerformanceMetrics(): Promise<SignalEnginePerformanceMetrics> {
    try {
      const response = await this.invokeWithTimeout<GetPerformanceMetricsResponse>(
        SIGNAL_ENGINE_IPC_CHANNELS.GET_PERFORMANCE_METRICS
      )

      if (!response.success || !hasResponseData(response)) {
        throw new Error(response.error || 'Failed to get performance metrics')
      }

      return response.data
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Failed to get performance metrics:', error)
      throw error
    }
  }

  /**
   * Create a strategy
   */
  public async createStrategy(
    indicators: string | string[],
    config: Record<string, unknown>
  ): Promise<Strategy> {
    try {
      const response = await this.invokeWithTimeout<CreateStrategyResponse>(
        SIGNAL_ENGINE_IPC_CHANNELS.CREATE_STRATEGY,
        indicators,
        config
      )

      if (!response.success || !hasResponseData(response)) {
        throw new Error(response.error || 'Failed to create strategy')
      }

      return response.data
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Failed to create strategy:', error)
      throw error
    }
  }

  /**
   * Remove a strategy
   */
  public async removeStrategy(strategyName: string): Promise<boolean> {
    try {
      const response = await this.invokeWithTimeout<RemoveStrategyResponse>(
        SIGNAL_ENGINE_IPC_CHANNELS.REMOVE_STRATEGY,
        strategyName
      )

      if (!response.success || !hasResponseData(response)) {
        return false
      }

      return response.data.removed
    } catch (error) {
      logger.error(LOG_CATEGORY, 'Failed to remove strategy:', error)
      return false
    }
  }

  /**
   * Set up event listeners for SignalEngine events
   */
  public setupEventListeners(handlers: SignalEngineEventHandlers): SignalEngineEventCleanup {
    const cleanupFunctions: (() => void)[] = []

    // Set up individual event listeners
    Object.entries(handlers).forEach(([handlerName, handler]) => {
      if (typeof handler === 'function') {
        const eventName = this.getEventNameFromHandler(handlerName)
        if (eventName) {
          const cleanup = window.api.on(`signal-engine:${eventName}`, handler)
          cleanupFunctions.push(cleanup)
          this.eventListeners.set(`${eventName}-${Date.now()}`, cleanup)
        }
      }
    })

    // Return cleanup function
    return () => {
      cleanupFunctions.forEach((cleanup) => cleanup())
      // Remove from internal tracking
      this.eventListeners.forEach((cleanup, key) => {
        if (cleanupFunctions.includes(cleanup)) {
          this.eventListeners.delete(key)
        }
      })
    }
  }

  /**
   * Dispose of the service and clean up resources
   */
  public dispose(): void {
    // Clean up all event listeners
    this.eventListeners.forEach((cleanup) => cleanup())
    this.eventListeners.clear()
    this.isInitialized = false

    if (this.config.enableDebugLogging) {
      logger.debug(LOG_CATEGORY, 'SignalEngineService disposed')
    }
  }

  /**
   * Helper method to invoke IPC with timeout
   */
  private async invokeWithTimeout<T>(channel: string, ...args: unknown[]): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`IPC call to ${channel} timed out after ${this.config.ipcTimeout}ms`))
      }, this.config.ipcTimeout)

      window.api
        .invoke<T>(channel, ...args)
        .then((response) => {
          clearTimeout(timeoutId)
          if (isSignalEngineIPCResponse(response)) {
            resolve(response)
          } else {
            reject(new Error('Invalid IPC response format'))
          }
        })
        .catch((error) => {
          clearTimeout(timeoutId)
          reject(error)
        })
    })
  }

  /**
   * Add event listener (mimics EventEmitter.on interface)
   */
  public on(event: string, handler: (...args: any[]) => void): () => void {
    const cleanup = window.api.on(`signal-engine:${event}`, handler)
    const key = `${event}-${Date.now()}-${Math.random()}`
    this.eventListeners.set(key, cleanup)

    return () => {
      cleanup()
      this.eventListeners.delete(key)
    }
  }

  /**
   * Remove event listener (mimics EventEmitter.off interface)
   */
  public off(event: string, handler: (...args: any[]) => void): void {
    // Note: This is a simplified implementation
    // In a real scenario, you'd need to track handlers more precisely
    logger.debug(LOG_CATEGORY, `Removing listener for event: ${event}`)
  }

  /**
   * Map handler names to event names
   */
  private getEventNameFromHandler(handlerName: string): string | null {
    const mapping: Record<string, string> = {
      onSignalGenerated: 'signal-generated',
      onStrategyCreated: 'strategy-created',
      onStrategyUpdated: 'strategy-updated',
      onStrategyDeleted: 'strategy-deleted',
      onPerformanceUpdate: 'performance-update',
      onConnectionStatus: 'connection-status',
      onError: 'error',
      onHeartbeat: 'heartbeat'
    }
    return mapping[handlerName] || null
  }
}

// Export singleton instance getter for convenience
export const signalEngineService = SignalEngineService.getInstance()

export default SignalEngineService
