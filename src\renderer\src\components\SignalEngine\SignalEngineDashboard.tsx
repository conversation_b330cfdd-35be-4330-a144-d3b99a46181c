/**
 * Signal Engine Dashboard Component
 * Main dashboard showing active strategies, real-time signals, and performance metrics
 */

import React, { useState, useEffect, useCallback } from 'react'
import { signalEngineService } from '../../services/signalEngineService'
import { useTradingContext } from '../../contexts/TradingContext'
import {
  useSignalEngineEvents,
  useSignalGeneratedEvent,
  usePerformanceUpdateEvent
} from '../../hooks/useWebSocketEvents'
import type {
  GeneratedSignal,
  Strategy,
  SignalEnginePerformanceMetrics
} from '../../../../shared/types/signals'

/**
 * Dashboard component props
 */
interface SignalEngineDashboardProps {
  /** Custom CSS classes */
  className?: string
  /** Dashboard refresh interval in milliseconds */
  refreshInterval?: number
  /** Maximum number of recent signals to display */
  maxRecentSignals?: number
}

/**
 * Dashboard state interface
 */
interface DashboardState {
  activeStrategies: Strategy[]
  recentSignals: GeneratedSignal[]
  performanceMetrics: SignalEnginePerformanceMetrics | null
  isLoading: boolean
  error: string | null
  lastUpdate: number
}

/**
 * Signal Engine Dashboard Component
 */
export const SignalEngineDashboard: React.FC<SignalEngineDashboardProps> = ({
  className = '',
  refreshInterval = 5000,
  maxRecentSignals = 10
}) => {
  // State management
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    activeStrategies: [],
    recentSignals: [],
    performanceMetrics: null,
    isLoading: true,
    error: null,
    lastUpdate: Date.now()
  })

  // Trading context
  const { isConnected, connectionStatus } = useTradingContext()

  /**
   * Update dashboard data via IPC
   */
  const updateDashboard = useCallback(async () => {
    try {
      setDashboardState((prev) => ({ ...prev, isLoading: true, error: null }))

      // Get active strategies via IPC
      const activeStrategies = await signalEngineService.getActiveStrategies()

      // Get recent signals via IPC
      const recentSignals = await signalEngineService.getRecentSignals(maxRecentSignals)

      // Get performance metrics via IPC
      const performanceMetrics = await signalEngineService.getPerformanceMetrics()

      setDashboardState({
        activeStrategies,
        recentSignals,
        performanceMetrics,
        isLoading: false,
        error: null,
        lastUpdate: Date.now()
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      setDashboardState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
    }
  }, [maxRecentSignals])

  /**
   * WebSocket event handlers for real-time updates
   */
  useSignalEngineEvents(
    {
      onSignalGenerated: useCallback(
        (signal: any) => {
          setDashboardState((prev) => ({
            ...prev,
            recentSignals: [signal, ...prev.recentSignals.slice(0, maxRecentSignals - 1)],
            lastUpdate: Date.now()
          }))
        },
        [maxRecentSignals]
      ),

      onPerformanceUpdate: useCallback((metrics: any) => {
        setDashboardState((prev) => ({
          ...prev,
          performanceMetrics: metrics.metrics,
          lastUpdate: Date.now()
        }))
      }, []),

      onStrategyCreated: useCallback((strategy: any) => {
        setDashboardState((prev) => ({
          ...prev,
          activeStrategies: [...prev.activeStrategies, strategy],
          lastUpdate: Date.now()
        }))
      }, []),

      onStrategyDeleted: useCallback((data: any) => {
        setDashboardState((prev) => ({
          ...prev,
          activeStrategies: prev.activeStrategies.filter((s) => s.name !== data.strategyId),
          lastUpdate: Date.now()
        }))
      }, [])
    },
    true
  )

  /**
   * Handle real-time signal updates
   */
  const handleSignalUpdate = useCallback(
    (signal: GeneratedSignal) => {
      setDashboardState((prev) => ({
        ...prev,
        recentSignals: [signal, ...prev.recentSignals.slice(0, maxRecentSignals - 1)],
        lastUpdate: Date.now()
      }))
    },
    [maxRecentSignals]
  )

  // Initialize dashboard and set up real-time updates
  useEffect(() => {
    updateDashboard()

    // Set up refresh interval
    const interval = setInterval(updateDashboard, refreshInterval)

    // Set up real-time signal listener via IPC
    const signalCleanup = signalEngineService.on('signal-generated', handleSignalUpdate)

    return () => {
      clearInterval(interval)
      signalCleanup()
    }
  }, [updateDashboard, refreshInterval, handleSignalUpdate])

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString()
  }

  /**
   * Get signal type color (green theme)
   */
  const getSignalColor = (signal: string): string => {
    switch (signal) {
      case 'BUY':
        return 'text-green-800 bg-green-200'
      case 'SELL':
        return 'text-orange-800 bg-orange-200'
      case 'HOLD':
        return 'text-green-600 bg-green-100'
      default:
        return 'text-green-600 bg-green-100'
    }
  }

  /**
   * Get confidence level color (green theme)
   */
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'text-green-700'
    if (confidence >= 0.6) return 'text-green-600'
    return 'text-green-500'
  }

  /**
   * Render loading state
   */
  if (dashboardState.isLoading && dashboardState.activeStrategies.length === 0) {
    return (
      <div className={`signal-engine-dashboard ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading Signal Engine Dashboard...</span>
        </div>
      </div>
    )
  }

  /**
   * Render error state
   */
  if (dashboardState.error) {
    return (
      <div className={`signal-engine-dashboard ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-red-600 text-xl mr-2">⚠️</span>
            <div>
              <h3 className="text-red-800 font-medium">Dashboard Error</h3>
              <p className="text-red-600 text-sm mt-1">{dashboardState.error}</p>
            </div>
          </div>
          <button
            onClick={updateDashboard}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`signal-engine-dashboard space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-2">
        <div className="flex items-center space-x-2">
          <h2 className="text-xl font-bold text-gray-900">📊 Signal Engine</h2>
          <div
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}
          >
            {connectionStatus}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={updateDashboard}
            className="w-8 h-8 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center text-sm transition-all duration-200 hover:shadow-lg"
            title="Refresh Dashboard"
          >
            🔄
          </button>
          <div className="text-xs text-gray-500">{formatTimestamp(dashboardState.lastUpdate)}</div>
        </div>
      </div>

      {/* Performance Metrics Overview */}
      {dashboardState.performanceMetrics && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-green-700">Signals</p>
                <p className="text-lg font-bold text-green-900">
                  {dashboardState.performanceMetrics.totalSignalsGenerated || 0}
                </p>
              </div>
              <div className="text-lg">🎯</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-green-700">Avg Time</p>
                <p className="text-lg font-bold text-green-900">
                  {(dashboardState.performanceMetrics.averageProcessingTime || 0).toFixed(1)}ms
                </p>
              </div>
              <div className="text-lg">⚡</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-green-700">Strategies</p>
                <p className="text-lg font-bold text-green-900">
                  {dashboardState.activeStrategies.length}
                </p>
              </div>
              <div className="text-lg">⚙️</div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-green-700">Total Time</p>
                <p className="text-lg font-bold text-green-900">
                  {(dashboardState.performanceMetrics.totalProcessingTime || 0).toFixed(0)}ms
                </p>
              </div>
              <div className="text-lg">📈</div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Active Strategies */}
        <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200">
          <div className="px-4 py-2 border-b border-green-200">
            <h3 className="text-md font-semibold text-green-900 flex items-center">
              <span className="mr-1">⚙️</span>
              Active Strategies
            </h3>
          </div>
          <div className="p-4">
            {dashboardState.activeStrategies.length === 0 ? (
              <div className="text-center py-6">
                <div className="text-3xl mb-2">🔍</div>
                <p className="text-green-600">No active strategies</p>
                <p className="text-xs text-green-500 mt-1">
                  Create a strategy to start generating signals
                </p>
                <button className="mt-3 w-8 h-8 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center text-sm transition-all duration-200 hover:shadow-lg mx-auto">
                  ➕
                </button>
              </div>
            ) : (
              <div className="space-y-2">
                {dashboardState.activeStrategies.map((strategy, index) => (
                  <div
                    key={strategy.id || index}
                    className="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-100"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-green-900 text-sm">{strategy.name}</h4>
                      <p className="text-xs text-green-600">
                        {strategy.description || 'No description'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          strategy.isReady
                            ? 'bg-green-200 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {strategy.isReady ? '✓' : '⏳'}
                      </div>
                      <button className="w-6 h-6 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center text-xs transition-all duration-200">
                        ⚙️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Recent Signals */}
        <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200">
          <div className="px-4 py-2 border-b border-green-200">
            <h3 className="text-md font-semibold text-green-900 flex items-center">
              <span className="mr-1">🎯</span>
              Recent Signals
            </h3>
          </div>
          <div className="p-4">
            {dashboardState.recentSignals.length === 0 ? (
              <div className="text-center py-6">
                <div className="text-3xl mb-2">📡</div>
                <p className="text-green-600">No recent signals</p>
                <p className="text-xs text-green-500 mt-1">
                  Signals will appear here when generated
                </p>
              </div>
            ) : (
              <div className="space-y-2 max-h-80 overflow-y-auto">
                {dashboardState.recentSignals.map((signal, index) => (
                  <div
                    key={`${signal.timestamp}-${index}`}
                    className="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-100"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-bold ${getSignalColor(signal.signal)}`}
                        >
                          {signal.signal}
                        </span>
                        <span className="text-sm font-medium text-green-900">
                          {signal.strategy}
                        </span>
                      </div>
                      <div className="flex items-center space-x-3 mt-1">
                        <span className="text-xs text-green-600">
                          {formatTimestamp(signal.timestamp)}
                        </span>
                        <span
                          className={`text-xs font-medium ${getConfidenceColor(signal.confidence)}`}
                        >
                          {(signal.confidence * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <button className="w-6 h-6 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center text-xs transition-all duration-200">
                      📊
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SignalEngineDashboard
