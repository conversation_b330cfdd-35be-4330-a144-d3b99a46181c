/**
 * Signal Engine Analytics Component
 * Provides comprehensive performance visualization and analytics
 * Following user preferences: green color scheme, circular buttons, minimal padding, WebSocket integration
 */

import React, { useState, useEffect, useCallback } from 'react'
import { signalEngineService } from '../../services/signalEngineService'
import { usePerformanceUpdateEvent, useSignalGeneratedEvent } from '../../hooks/useWebSocketEvents'
import type {
  SignalEnginePerformanceMetrics,
  GeneratedSignal
} from '../../../../shared/types/signals'

interface SignalEngineAnalyticsProps {
  className?: string
}

interface AnalyticsData {
  performanceMetrics: SignalEnginePerformanceMetrics | null
  signalHistory: GeneratedSignal[]
  signalStats: {
    totalSignals: number
    buySignals: number
    sellSignals: number
    holdSignals: number
    averageConfidence: number
    signalsPerHour: number
  }
  isLoading: boolean
  error: string | null
}

/**
 * Signal Engine Analytics Component
 */
export const SignalEngineAnalytics: React.FC<SignalEngineAnalyticsProps> = ({ className = '' }) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    performanceMetrics: null,
    signalHistory: [],
    signalStats: {
      totalSignals: 0,
      buySignals: 0,
      sellSignals: 0,
      holdSignals: 0,
      averageConfidence: 0,
      signalsPerHour: 0
    },
    isLoading: true,
    error: null
  })

  /**
   * Calculate signal statistics
   */
  const calculateSignalStats = useCallback((signals: GeneratedSignal[]) => {
    if (signals.length === 0) {
      return {
        totalSignals: 0,
        buySignals: 0,
        sellSignals: 0,
        holdSignals: 0,
        averageConfidence: 0,
        signalsPerHour: 0
      }
    }

    const buySignals = signals.filter((s) => s.signal === 'BUY').length
    const sellSignals = signals.filter((s) => s.signal === 'SELL').length
    const holdSignals = signals.filter((s) => s.signal === 'HOLD').length
    const totalConfidence = signals.reduce((sum, s) => sum + s.confidence, 0)
    const averageConfidence = totalConfidence / signals.length

    // Calculate signals per hour (last 24 hours)
    const now = Date.now()
    const oneDayAgo = now - 24 * 60 * 60 * 1000
    const recentSignals = signals.filter((s) => s.timestamp >= oneDayAgo)
    const signalsPerHour = recentSignals.length / 24

    return {
      totalSignals: signals.length,
      buySignals,
      sellSignals,
      holdSignals,
      averageConfidence,
      signalsPerHour
    }
  }, [])

  /**
   * Update analytics data
   */
  const updateAnalytics = useCallback(async () => {
    try {
      setAnalyticsData((prev) => ({ ...prev, isLoading: true, error: null }))

      const performanceMetrics = await signalEngineService.getPerformanceMetrics()
      const signalHistory = await signalEngineService.getRecentSignals(100) // Get last 100 signals
      const signalStats = calculateSignalStats(signalHistory)

      setAnalyticsData({
        performanceMetrics,
        signalHistory,
        signalStats,
        isLoading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load analytics'
      setAnalyticsData((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
    }
  }, [calculateSignalStats])

  /**
   * WebSocket event handlers for real-time updates
   */
  usePerformanceUpdateEvent(
    useCallback((data: any) => {
      setAnalyticsData((prev) => ({
        ...prev,
        performanceMetrics: data.metrics
      }))
    }, []),
    true
  )

  useSignalGeneratedEvent(
    useCallback(
      (signal: any) => {
        setAnalyticsData((prev) => {
          const newSignalHistory = [signal, ...prev.signalHistory.slice(0, 99)]
          const newSignalStats = calculateSignalStats(newSignalHistory)

          return {
            ...prev,
            signalHistory: newSignalHistory,
            signalStats: newSignalStats
          }
        })
      },
      [calculateSignalStats]
    ),
    true
  )

  /**
   * Initialize analytics
   */
  useEffect(() => {
    updateAnalytics()
  }, [updateAnalytics])

  /**
   * Format percentage
   */
  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`
  }

  /**
   * Format number with commas
   */
  const formatNumber = (value: number): string => {
    return value.toLocaleString()
  }

  /**
   * Get signal distribution chart data
   */
  const getSignalDistribution = () => {
    const { buySignals, sellSignals, holdSignals, totalSignals } = analyticsData.signalStats

    if (totalSignals === 0) return []

    return [
      {
        label: 'BUY',
        value: buySignals,
        percentage: (buySignals / totalSignals) * 100,
        color: 'bg-green-500'
      },
      {
        label: 'SELL',
        value: sellSignals,
        percentage: (sellSignals / totalSignals) * 100,
        color: 'bg-orange-500'
      },
      {
        label: 'HOLD',
        value: holdSignals,
        percentage: (holdSignals / totalSignals) * 100,
        color: 'bg-green-300'
      }
    ]
  }

  if (analyticsData.isLoading) {
    return (
      <div className={`signal-engine-analytics ${className}`}>
        <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200 p-4">
          <div className="text-center py-8">
            <div className="text-3xl mb-2">⏳</div>
            <p className="text-green-600">Loading analytics...</p>
          </div>
        </div>
      </div>
    )
  }

  if (analyticsData.error) {
    return (
      <div className={`signal-engine-analytics ${className}`}>
        <div className="bg-gradient-to-br from-red-50 to-white rounded-lg border border-red-200 p-4">
          <div className="text-center py-8">
            <div className="text-3xl mb-2">❌</div>
            <p className="text-red-600">{analyticsData.error}</p>
            <button
              onClick={updateAnalytics}
              className="mt-3 w-8 h-8 rounded-full bg-red-500 hover:bg-red-600 text-white flex items-center justify-center text-sm transition-all duration-200 hover:shadow-lg mx-auto"
            >
              🔄
            </button>
          </div>
        </div>
      </div>
    )
  }

  const signalDistribution = getSignalDistribution()

  return (
    <div className={`signal-engine-analytics space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-2">
        <h3 className="text-md font-semibold text-green-900 flex items-center">
          <span className="mr-1">📊</span>
          Analytics
        </h3>
        <button
          onClick={updateAnalytics}
          className="w-8 h-8 rounded-full bg-green-500 hover:bg-green-600 text-white flex items-center justify-center text-sm transition-all duration-200 hover:shadow-lg"
          title="Refresh Analytics"
        >
          🔄
        </button>
      </div>

      {/* Signal Statistics */}
      <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200 p-4">
        <h4 className="text-sm font-semibold text-green-900 mb-3">📈 Signal Statistics</h4>
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center">
            <div className="text-lg font-bold text-green-900">
              {formatNumber(analyticsData.signalStats.totalSignals)}
            </div>
            <div className="text-xs text-green-600">Total Signals</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-900">
              {formatPercentage(analyticsData.signalStats.averageConfidence)}
            </div>
            <div className="text-xs text-green-600">Avg Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-900">
              {analyticsData.signalStats.signalsPerHour.toFixed(1)}
            </div>
            <div className="text-xs text-green-600">Signals/Hour</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-900">
              {analyticsData.performanceMetrics?.averageProcessingTime.toFixed(1) || 0}ms
            </div>
            <div className="text-xs text-green-600">Avg Processing</div>
          </div>
        </div>
      </div>

      {/* Signal Distribution */}
      <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200 p-4">
        <h4 className="text-sm font-semibold text-green-900 mb-3">🎯 Signal Distribution</h4>
        {signalDistribution.length > 0 ? (
          <div className="space-y-2">
            {signalDistribution.map((item) => (
              <div key={item.label} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                  <span className="text-sm text-green-900">{item.label}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-green-900">{item.value}</span>
                  <span className="text-xs text-green-600">({item.percentage.toFixed(1)}%)</span>
                </div>
              </div>
            ))}

            {/* Visual bar chart */}
            <div className="mt-3 h-4 bg-green-100 rounded-full overflow-hidden flex">
              {signalDistribution.map((item, index) => (
                <div
                  key={index}
                  className={`${item.color} transition-all duration-500`}
                  style={{ width: `${item.percentage}%` }}
                  title={`${item.label}: ${item.percentage.toFixed(1)}%`}
                ></div>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-4">
            <div className="text-2xl mb-1">📊</div>
            <p className="text-green-600 text-sm">No signal data available</p>
          </div>
        )}
      </div>

      {/* Performance Metrics */}
      {analyticsData.performanceMetrics && (
        <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200 p-4">
          <h4 className="text-sm font-semibold text-green-900 mb-3">⚡ Performance Metrics</h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center">
              <div className="text-lg font-bold text-green-900">
                {formatNumber(analyticsData.performanceMetrics.totalProcessingTime)}ms
              </div>
              <div className="text-xs text-green-600">Total Processing</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-900">
                {(analyticsData.performanceMetrics.memoryUsage / 1024 / 1024).toFixed(1)}MB
              </div>
              <div className="text-xs text-green-600">Memory Usage</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-900">
                {analyticsData.performanceMetrics.strategiesCreated}
              </div>
              <div className="text-xs text-green-600">Strategies Created</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-900">
                {analyticsData.performanceMetrics.strategiesRemoved}
              </div>
              <div className="text-xs text-green-600">Strategies Removed</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SignalEngineAnalytics
