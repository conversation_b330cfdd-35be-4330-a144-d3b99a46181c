/**
 * Strategy Builder Interface Component
 * Interactive form for creating and configuring single and multi-indicator strategies
 */

import React, { useState, useCallback } from 'react'
import { signalEngineService } from '../../services/signalEngineService'
import { NumericInput } from '../Common/NumericInput'
import type {
  StrategyConfig,
  SingleIndicatorStrategyConfig,
  MultiIndicatorStrategyConfig
} from '../../../../shared/types/signals'

/**
 * Strategy builder component props
 */
interface StrategyBuilderProps {
  /** Custom CSS classes */
  className?: string
  /** Callback when strategy is created */
  onStrategyCreated?: (strategy: StrategyConfig) => void
  /** Callback when builder is closed */
  onClose?: () => void
}

/**
 * Available indicators for strategy building
 */
const AVAILABLE_INDICATORS = [
  { id: 'rsi', name: 'RSI (Relative Strength Index)', description: 'Momentum oscillator' },
  { id: 'sma', name: 'SMA (Simple Moving Average)', description: 'Trend following indicator' },
  { id: 'bollingerbands', name: 'Bollinger Bands', description: 'Volatility indicator' }
] as const

/**
 * Strategy types
 */
type StrategyType = 'single' | 'multi'

/**
 * Combination logic options
 */
const COMBINATION_LOGIC_OPTIONS = [
  { value: 'AND', label: 'AND (All conditions must be met)', description: 'Conservative approach' },
  { value: 'OR', label: 'OR (Any condition can trigger)', description: 'Aggressive approach' },
  { value: 'MAJORITY', label: 'MAJORITY (Most conditions)', description: 'Balanced approach' },
  { value: 'WEIGHTED', label: 'WEIGHTED (Custom weights)', description: 'Advanced approach' }
] as const

/**
 * Strategy Builder Component
 */
export const StrategyBuilder: React.FC<StrategyBuilderProps> = ({
  className = '',
  onStrategyCreated,
  onClose
}) => {
  // Form state
  const [strategyType, setStrategyType] = useState<StrategyType>('single')
  const [strategyName, setStrategyName] = useState('')
  const [strategyDescription, setStrategyDescription] = useState('')
  const [selectedIndicators, setSelectedIndicators] = useState<string[]>(['rsi'])
  const [combinationLogic, setCombinationLogic] = useState<'AND' | 'OR' | 'MAJORITY' | 'WEIGHTED'>(
    'AND'
  )

  // Indicator configurations
  const [rsiConfig, setRsiConfig] = useState({
    period: 14,
    overbought: 70,
    oversold: 30
  })

  const [smaConfig, setSmaConfig] = useState({
    period: 20
  })

  const [bollingerBandsConfig, setBollingerBandsConfig] = useState({
    period: 20,
    standardDeviations: 2
  })

  // Weights for weighted strategy
  const [weights, setWeights] = useState<Record<string, number>>({
    rsi: 0.5,
    sma: 0.3,
    bollingerbands: 0.2
  })

  // Form validation and submission state
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showPreview, setShowPreview] = useState(false)
  const [previewData, setPreviewData] = useState<any>(null)

  /**
   * Validate form inputs
   */
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {}

    if (!strategyName.trim()) {
      newErrors.name = 'Strategy name is required'
    }

    if (selectedIndicators.length === 0) {
      newErrors.indicators = 'At least one indicator must be selected'
    }

    if (strategyType === 'multi' && selectedIndicators.length < 2) {
      newErrors.indicators = 'Multi-indicator strategy requires at least 2 indicators'
    }

    // Validate weights for weighted strategy
    if (combinationLogic === 'WEIGHTED') {
      const totalWeight = selectedIndicators.reduce(
        (sum, indicator) => sum + (weights[indicator] || 0),
        0
      )
      if (Math.abs(totalWeight - 1) > 0.01) {
        newErrors.weights = 'Weights must sum to 1.0'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [strategyName, selectedIndicators, strategyType, combinationLogic, weights])

  /**
   * Generate strategy preview
   */
  const generatePreview = useCallback(async () => {
    try {
      const strategyConfig = createStrategyConfig()
      // Create a temporary strategy for preview via IPC
      let tempStrategy
      if (strategyType === 'single') {
        tempStrategy = await signalEngineService.createStrategy(
          selectedIndicators[0],
          strategyConfig
        )
      } else {
        tempStrategy = await signalEngineService.createStrategy(selectedIndicators, strategyConfig)
      }

      if (tempStrategy) {
        // Generate sample data for preview
        const sampleData = Array.from({ length: 50 }, (_, i) => ({
          value: 100 + Math.sin(i * 0.1) * 10 + Math.random() * 5,
          timestamp: Date.now() + i * 1000
        }))

        const previewSignals = []
        for (const dataPoint of sampleData) {
          const signal = tempStrategy.addData(dataPoint)
          if (signal) {
            previewSignals.push(signal)
          }
        }

        setPreviewData({
          strategy: tempStrategy.name,
          indicators: selectedIndicators,
          sampleSignals: previewSignals.slice(0, 5), // Show first 5 signals
          totalSignals: previewSignals.length,
          avgConfidence:
            previewSignals.length > 0
              ? previewSignals.reduce((sum, s) => sum + s.confidence, 0) / previewSignals.length
              : 0
        })

        // Clean up temporary strategy via IPC
        await signalEngineService.removeStrategy(tempStrategy.name)
      }
    } catch (error) {
      console.error('Preview generation failed:', error)
      setPreviewData(null)
    }
  }, [strategyType, selectedIndicators, createStrategyConfig])

  /**
   * Enhanced indicator configuration validation
   */
  const validateIndicatorConfig = useCallback((indicator: string, config: any): string[] => {
    const errors: string[] = []

    switch (indicator) {
      case 'rsi':
        if (config.period < 2 || config.period > 100) {
          errors.push('RSI period must be between 2 and 100')
        }
        if (config.overbought <= config.oversold) {
          errors.push('Overbought threshold must be greater than oversold threshold')
        }
        if (config.overbought > 100 || config.oversold < 0) {
          errors.push('RSI thresholds must be between 0 and 100')
        }
        break

      case 'sma':
        if (config.period < 2 || config.period > 200) {
          errors.push('SMA period must be between 2 and 200')
        }
        break

      case 'bollingerbands':
        if (config.period < 2 || config.period > 100) {
          errors.push('Bollinger Bands period must be between 2 and 100')
        }
        if (config.standardDeviations < 0.5 || config.standardDeviations > 5) {
          errors.push('Standard deviations must be between 0.5 and 5')
        }
        break
    }

    return errors
  }, [])

  /**
   * Real-time validation as user types
   */
  const validateRealTime = useCallback(() => {
    const newErrors: Record<string, string> = {}

    // Validate each selected indicator configuration
    selectedIndicators.forEach((indicator) => {
      let config
      switch (indicator) {
        case 'rsi':
          config = rsiConfig
          break
        case 'sma':
          config = smaConfig
          break
        case 'bollingerbands':
          config = bollingerBandsConfig
          break
      }

      if (config) {
        const indicatorErrors = validateIndicatorConfig(indicator, config)
        if (indicatorErrors.length > 0) {
          newErrors[indicator] = indicatorErrors.join(', ')
        }
      }
    })

    setErrors((prev) => ({ ...prev, ...newErrors }))
  }, [selectedIndicators, rsiConfig, smaConfig, bollingerBandsConfig, validateIndicatorConfig])

  // Run real-time validation when configurations change
  useEffect(() => {
    validateRealTime()
  }, [validateRealTime])

  /**
   * Handle indicator selection
   */
  const handleIndicatorToggle = useCallback((indicatorId: string) => {
    setSelectedIndicators((prev) => {
      if (prev.includes(indicatorId)) {
        return prev.filter((id) => id !== indicatorId)
      } else {
        return [...prev, indicatorId]
      }
    })
  }, [])

  /**
   * Handle weight change
   */
  const handleWeightChange = useCallback((indicator: string, weight: number) => {
    setWeights((prev) => ({
      ...prev,
      [indicator]: weight
    }))
  }, [])

  /**
   * Create strategy configuration
   */
  const createStrategyConfig = useCallback((): StrategyConfig => {
    const baseConfig = {
      name: strategyName.trim(),
      description: strategyDescription.trim() || undefined
    }

    if (strategyType === 'single') {
      const indicator = selectedIndicators[0]
      let indicatorConfig: any = {}

      switch (indicator) {
        case 'rsi':
          indicatorConfig = rsiConfig
          break
        case 'sma':
          indicatorConfig = smaConfig
          break
        case 'bollingerbands':
          indicatorConfig = bollingerBandsConfig
          break
      }

      const singleConfig: SingleIndicatorStrategyConfig = {
        ...baseConfig,
        indicatorConfig,
        signalRules: {
          buyConditions: [
            {
              type: 'threshold',
              params: { threshold: indicator === 'rsi' ? 30 : 'sma', operator: 'lt' }
            }
          ],
          sellConditions: [
            {
              type: 'threshold',
              params: { threshold: indicator === 'rsi' ? 70 : 'sma', operator: 'gt' }
            }
          ]
        }
      }

      return singleConfig
    } else {
      const indicatorConfigs: Record<string, any> = {}

      selectedIndicators.forEach((indicator) => {
        switch (indicator) {
          case 'rsi':
            indicatorConfigs.rsi = rsiConfig
            break
          case 'sma':
            indicatorConfigs.sma = smaConfig
            break
          case 'bollingerbands':
            indicatorConfigs.bollingerbands = bollingerBandsConfig
            break
        }
      })

      const multiConfig: MultiIndicatorStrategyConfig = {
        ...baseConfig,
        indicatorConfigs,
        signalRules: {
          buyConditions: [
            {
              type: 'threshold',
              params: { threshold: 30, operator: 'lt' }
            }
          ],
          sellConditions: [
            {
              type: 'threshold',
              params: { threshold: 70, operator: 'gt' }
            }
          ]
        },
        combinationLogic,
        weights:
          combinationLogic === 'WEIGHTED'
            ? selectedIndicators.map((indicator) => ({ indicator, weight: weights[indicator] }))
            : undefined
      }

      return multiConfig
    }
  }, [
    strategyName,
    strategyDescription,
    strategyType,
    selectedIndicators,
    combinationLogic,
    rsiConfig,
    smaConfig,
    bollingerBandsConfig,
    weights
  ])

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()

      if (!validateForm()) {
        return
      }

      setIsSubmitting(true)

      try {
        const strategyConfig = createStrategyConfig()

        let strategy
        if (strategyType === 'single') {
          strategy = await signalEngineService.createStrategy(selectedIndicators[0], strategyConfig)
        } else {
          strategy = await signalEngineService.createStrategy(selectedIndicators, strategyConfig)
        }

        if (strategy) {
          onStrategyCreated?.(strategyConfig)

          // Reset form
          setStrategyName('')
          setStrategyDescription('')
          setSelectedIndicators(['rsi'])
          setStrategyType('single')
          setCombinationLogic('AND')
          setErrors({})
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create strategy'
        setErrors({ submit: errorMessage })
      } finally {
        setIsSubmitting(false)
      }
    },
    [validateForm, createStrategyConfig, strategyType, selectedIndicators, onStrategyCreated]
  )

  return (
    <div className={`strategy-builder bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <span className="mr-2">⚙️</span>
          Strategy Builder
        </h3>
        {onClose && (
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
            ✕
          </button>
        )}
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Strategy Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Strategy Type</label>
          <div className="grid grid-cols-2 gap-3">
            <button
              type="button"
              onClick={() => setStrategyType('single')}
              className={`p-3 rounded-lg border-2 transition-colors ${
                strategyType === 'single'
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-sm font-medium">Single Indicator</div>
              <div className="text-xs text-gray-500 mt-1">Use one indicator</div>
            </button>
            <button
              type="button"
              onClick={() => setStrategyType('multi')}
              className={`p-3 rounded-lg border-2 transition-colors ${
                strategyType === 'multi'
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-sm font-medium">Multi-Indicator</div>
              <div className="text-xs text-gray-500 mt-1">Combine multiple indicators</div>
            </button>
          </div>
        </div>

        {/* Strategy Name */}
        <div>
          <label htmlFor="strategyName" className="block text-sm font-medium text-gray-700 mb-2">
            Strategy Name *
          </label>
          <input
            id="strategyName"
            type="text"
            value={strategyName}
            onChange={(e) => setStrategyName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Enter strategy name"
          />
          {errors.name && <p className="text-red-600 text-sm mt-1">{errors.name}</p>}
        </div>

        {/* Strategy Description */}
        <div>
          <label
            htmlFor="strategyDescription"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Description
          </label>
          <textarea
            id="strategyDescription"
            value={strategyDescription}
            onChange={(e) => setStrategyDescription(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Describe your strategy (optional)"
          />
        </div>

        {/* Indicator Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Indicators *
          </label>
          <div className="space-y-2">
            {AVAILABLE_INDICATORS.map((indicator) => (
              <label
                key={indicator.id}
                className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
              >
                <input
                  type={strategyType === 'single' ? 'radio' : 'checkbox'}
                  name={strategyType === 'single' ? 'indicator' : undefined}
                  checked={selectedIndicators.includes(indicator.id)}
                  onChange={() => handleIndicatorToggle(indicator.id)}
                  className="mr-3"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{indicator.name}</div>
                  <div className="text-sm text-gray-500">{indicator.description}</div>
                </div>
              </label>
            ))}
          </div>
          {errors.indicators && <p className="text-red-600 text-sm mt-1">{errors.indicators}</p>}
        </div>

        {/* Multi-Indicator Options */}
        {strategyType === 'multi' && selectedIndicators.length > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Combination Logic
            </label>
            <div className="space-y-2">
              {COMBINATION_LOGIC_OPTIONS.map((option) => (
                <label
                  key={option.value}
                  className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                >
                  <input
                    type="radio"
                    name="combinationLogic"
                    value={option.value}
                    checked={combinationLogic === option.value}
                    onChange={(e) => setCombinationLogic(e.target.value as any)}
                    className="mr-3"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{option.label}</div>
                    <div className="text-sm text-gray-500">{option.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Weighted Strategy Configuration */}
        {combinationLogic === 'WEIGHTED' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Indicator Weights
            </label>
            <div className="space-y-3">
              {selectedIndicators.map((indicator) => (
                <div key={indicator} className="flex items-center space-x-3">
                  <div className="flex-1">
                    <label className="text-sm text-gray-600">
                      {AVAILABLE_INDICATORS.find((i) => i.id === indicator)?.name}
                    </label>
                  </div>
                  <div className="w-24">
                    <NumericInput
                      value={weights[indicator] || 0}
                      onChange={(value) => handleWeightChange(indicator, value)}
                      min={0}
                      max={1}
                      step={0.1}
                      className="text-sm"
                    />
                  </div>
                </div>
              ))}
              <div className="text-sm text-gray-500">
                Total weight:{' '}
                {selectedIndicators
                  .reduce((sum, indicator) => sum + (weights[indicator] || 0), 0)
                  .toFixed(1)}
              </div>
            </div>
            {errors.weights && <p className="text-red-600 text-sm mt-1">{errors.weights}</p>}
          </div>
        )}

        {/* Indicator Configuration */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-700">Indicator Configuration</h4>

          {/* RSI Configuration */}
          {selectedIndicators.includes('rsi') && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h5 className="font-medium text-gray-900 mb-3">RSI Settings</h5>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Period</label>
                  <NumericInput
                    value={rsiConfig.period}
                    onChange={(value) => setRsiConfig((prev) => ({ ...prev, period: value }))}
                    min={2}
                    max={100}
                    className="text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Overbought</label>
                  <NumericInput
                    value={rsiConfig.overbought}
                    onChange={(value) => setRsiConfig((prev) => ({ ...prev, overbought: value }))}
                    min={50}
                    max={100}
                    className="text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Oversold</label>
                  <NumericInput
                    value={rsiConfig.oversold}
                    onChange={(value) => setRsiConfig((prev) => ({ ...prev, oversold: value }))}
                    min={0}
                    max={50}
                    className="text-sm"
                  />
                </div>
              </div>
            </div>
          )}

          {/* SMA Configuration */}
          {selectedIndicators.includes('sma') && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h5 className="font-medium text-gray-900 mb-3">SMA Settings</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Period</label>
                  <NumericInput
                    value={smaConfig.period}
                    onChange={(value) => setSmaConfig((prev) => ({ ...prev, period: value }))}
                    min={2}
                    max={200}
                    className="text-sm"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Bollinger Bands Configuration */}
          {selectedIndicators.includes('bollingerbands') && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h5 className="font-medium text-gray-900 mb-3">Bollinger Bands Settings</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Period</label>
                  <NumericInput
                    value={bollingerBandsConfig.period}
                    onChange={(value) =>
                      setBollingerBandsConfig((prev) => ({ ...prev, period: value }))
                    }
                    min={2}
                    max={100}
                    className="text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Standard Deviations</label>
                  <NumericInput
                    value={bollingerBandsConfig.standardDeviations}
                    onChange={(value) =>
                      setBollingerBandsConfig((prev) => ({ ...prev, standardDeviations: value }))
                    }
                    min={0.5}
                    max={5}
                    step={0.1}
                    className="text-sm"
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Strategy Preview */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">Strategy Preview</h4>
            <button
              type="button"
              onClick={() => {
                if (showPreview) {
                  setShowPreview(false)
                } else {
                  generatePreview()
                  setShowPreview(true)
                }
              }}
              disabled={!validateForm()}
              className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {showPreview ? '🔼 Hide Preview' : '🔽 Show Preview'}
            </button>
          </div>

          {showPreview && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              {previewData ? (
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Strategy:</span>
                      <span className="ml-2 text-gray-900">{previewData.strategy}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Indicators:</span>
                      <span className="ml-2 text-gray-900">
                        {previewData.indicators.join(', ')}
                      </span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Sample Signals:</span>
                      <span className="ml-2 text-gray-900">{previewData.totalSignals}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Avg Confidence:</span>
                      <span className="ml-2 text-gray-900">
                        {(previewData.avgConfidence * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>

                  {previewData.sampleSignals.length > 0 && (
                    <div>
                      <h6 className="font-medium text-gray-700 mb-2">Recent Signals:</h6>
                      <div className="space-y-1">
                        {previewData.sampleSignals.map((signal: any, index: number) => (
                          <div
                            key={index}
                            className="flex items-center justify-between text-xs bg-white rounded px-2 py-1"
                          >
                            <span
                              className={`font-medium ${
                                signal.signal === 'BUY'
                                  ? 'text-green-600'
                                  : signal.signal === 'SELL'
                                    ? 'text-red-600'
                                    : 'text-gray-600'
                              }`}
                            >
                              {signal.signal}
                            </span>
                            <span className="text-gray-500">
                              {(signal.confidence * 100).toFixed(1)}% confidence
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600">Generating preview...</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Error Display */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <span className="text-red-600 text-xl mr-2">⚠️</span>
              <p className="text-red-600">{errors.submit}</p>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <span className="mr-2">🎯</span>
                Create Strategy
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  )
}

export default StrategyBuilder
