/**
 * Signal Engine Manager Component
 * Provides comprehensive strategy creation and management interface
 * Following user preferences: green color scheme, circular buttons, minimal padding, WebSocket integration
 */

import React, { useState, useCallback } from 'react'
import { signalEngineService } from '../../services/signalEngineService'
import { useSignalEngineEvents } from '../../hooks/useWebSocketEvents'
import type { Strategy } from '../../../../shared/types/signals'

interface SignalEngineManagerProps {
  className?: string
  onStrategyCreated?: (strategy: Strategy) => void
  onStrategyDeleted?: (strategyId: string) => void
}

interface StrategyFormData {
  name: string
  description: string
  type: 'rsi' | 'sma' | 'bollingerbands' | 'multi'
  config: Record<string, any>
}

/**
 * Signal Engine Manager Component
 */
export const SignalEngineManager: React.FC<SignalEngineManagerProps> = ({
  className = '',
  onStrategyCreated,
  onStrategyDeleted
}) => {
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState<StrategyFormData>({
    name: '',
    description: '',
    type: 'rsi',
    config: {}
  })
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  /**
   * WebSocket event handlers
   */
  useSignalEngineEvents(
    {
      onStrategyCreated: useCallback(
        (strategy: any) => {
          setSuccess(`Strategy "${strategy.name}" created successfully! 🎉`)
          onStrategyCreated?.(strategy)
          setTimeout(() => setSuccess(null), 3000)
        },
        [onStrategyCreated]
      ),

      onStrategyDeleted: useCallback(
        (data: any) => {
          setSuccess(`Strategy "${data.strategyId}" deleted successfully! 🗑️`)
          onStrategyDeleted?.(data.strategyId)
          setTimeout(() => setSuccess(null), 3000)
        },
        [onStrategyDeleted]
      ),

      onError: useCallback((error: any) => {
        setError(`Signal Engine Error: ${error.error}`)
        setTimeout(() => setError(null), 5000)
      }, [])
    },
    true
  )

  /**
   * Handle form input changes
   */
  const handleInputChange = useCallback((field: keyof StrategyFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }))
    setError(null)
  }, [])

  /**
   * Handle config changes
   */
  const handleConfigChange = useCallback((key: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      config: {
        ...prev.config,
        [key]: value
      }
    }))
  }, [])

  /**
   * Create strategy
   */
  const createStrategy = useCallback(async () => {
    if (!formData.name.trim()) {
      setError('Strategy name is required')
      return
    }

    setIsCreating(true)
    setError(null)

    try {
      const signalEngine = SignalEngine.getInstance()

      const config = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        ...formData.config
      }

      const strategy = signalEngine.createStrategy(formData.type, config)

      // Reset form
      setFormData({
        name: '',
        description: '',
        type: 'rsi',
        config: {}
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create strategy'
      setError(errorMessage)
    } finally {
      setIsCreating(false)
    }
  }, [formData])

  /**
   * Get default config for strategy type
   */
  const getDefaultConfig = useCallback((type: string) => {
    switch (type) {
      case 'rsi':
        return {
          period: 14,
          overboughtThreshold: 70,
          oversoldThreshold: 30
        }
      case 'sma':
        return {
          period: 20
        }
      case 'bollingerbands':
        return {
          period: 20,
          standardDeviations: 2
        }
      default:
        return {}
    }
  }, [])

  /**
   * Handle strategy type change
   */
  const handleTypeChange = useCallback(
    (type: StrategyFormData['type']) => {
      setFormData((prev) => ({
        ...prev,
        type,
        config: getDefaultConfig(type)
      }))
    },
    [getDefaultConfig]
  )

  /**
   * Render strategy type selector
   */
  const renderTypeSelector = () => (
    <div className="space-y-2">
      <label className="text-sm font-medium text-green-900">Strategy Type</label>
      <div className="grid grid-cols-2 gap-2">
        {[
          { value: 'rsi', label: 'RSI', emoji: '📊' },
          { value: 'sma', label: 'SMA', emoji: '📈' },
          { value: 'bollingerbands', label: 'Bollinger', emoji: '📉' },
          { value: 'multi', label: 'Multi', emoji: '🔧' }
        ].map(({ value, label, emoji }) => (
          <button
            key={value}
            onClick={() => handleTypeChange(value as StrategyFormData['type'])}
            className={`p-2 rounded-lg border text-sm font-medium transition-all duration-200 ${
              formData.type === value
                ? 'bg-green-200 border-green-400 text-green-900'
                : 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
            }`}
          >
            {emoji} {label}
          </button>
        ))}
      </div>
    </div>
  )

  /**
   * Render config inputs based on strategy type
   */
  const renderConfigInputs = () => {
    const config = formData.config

    switch (formData.type) {
      case 'rsi':
        return (
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-green-900">Period</label>
              <input
                type="number"
                value={config.period || 14}
                onChange={(e) => handleConfigChange('period', parseInt(e.target.value))}
                className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none"
                min="1"
                max="100"
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs font-medium text-green-900">Overbought</label>
                <input
                  type="number"
                  value={config.overboughtThreshold || 70}
                  onChange={(e) =>
                    handleConfigChange('overboughtThreshold', parseInt(e.target.value))
                  }
                  className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none"
                  min="50"
                  max="100"
                />
              </div>
              <div>
                <label className="text-xs font-medium text-green-900">Oversold</label>
                <input
                  type="number"
                  value={config.oversoldThreshold || 30}
                  onChange={(e) =>
                    handleConfigChange('oversoldThreshold', parseInt(e.target.value))
                  }
                  className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none"
                  min="0"
                  max="50"
                />
              </div>
            </div>
          </div>
        )

      case 'sma':
        return (
          <div>
            <label className="text-xs font-medium text-green-900">Period</label>
            <input
              type="number"
              value={config.period || 20}
              onChange={(e) => handleConfigChange('period', parseInt(e.target.value))}
              className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none"
              min="1"
              max="200"
            />
          </div>
        )

      case 'bollingerbands':
        return (
          <div className="space-y-3">
            <div>
              <label className="text-xs font-medium text-green-900">Period</label>
              <input
                type="number"
                value={config.period || 20}
                onChange={(e) => handleConfigChange('period', parseInt(e.target.value))}
                className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none"
                min="1"
                max="100"
              />
            </div>
            <div>
              <label className="text-xs font-medium text-green-900">Standard Deviations</label>
              <input
                type="number"
                step="0.1"
                value={config.standardDeviations || 2}
                onChange={(e) =>
                  handleConfigChange('standardDeviations', parseFloat(e.target.value))
                }
                className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none"
                min="0.5"
                max="5"
              />
            </div>
          </div>
        )

      default:
        return (
          <div className="text-center py-4">
            <p className="text-green-600 text-sm">Multi-indicator strategies coming soon! 🚀</p>
          </div>
        )
    }
  }

  return (
    <div className={`signal-engine-manager ${className}`}>
      <div className="bg-gradient-to-br from-green-50 to-white rounded-lg border border-green-200 p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-md font-semibold text-green-900 flex items-center">
            <span className="mr-1">⚙️</span>
            Strategy Manager
          </h3>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-3 p-2 bg-green-100 border border-green-200 rounded text-sm text-green-800">
            {success}
          </div>
        )}
        {error && (
          <div className="mb-3 p-2 bg-red-100 border border-red-200 rounded text-sm text-red-800">
            {error}
          </div>
        )}

        {/* Strategy Creation Form */}
        <div className="space-y-4">
          {/* Basic Info */}
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-green-900">Strategy Name</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter strategy name..."
                className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-green-900">Description</label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Optional description..."
                rows={2}
                className="w-full p-2 border border-green-200 rounded text-sm focus:border-green-400 focus:outline-none resize-none"
              />
            </div>
          </div>

          {/* Strategy Type */}
          {renderTypeSelector()}

          {/* Configuration */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-green-900">Configuration</label>
            <div className="bg-green-50 border border-green-200 rounded p-3">
              {renderConfigInputs()}
            </div>
          </div>

          {/* Create Button */}
          <button
            onClick={createStrategy}
            disabled={isCreating || !formData.name.trim()}
            className={`w-full h-10 rounded-full font-medium text-sm transition-all duration-200 ${
              isCreating || !formData.name.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-green-500 hover:bg-green-600 text-white hover:shadow-lg'
            }`}
          >
            {isCreating ? '⏳ Creating...' : '🚀 Create Strategy'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default SignalEngineManager
