/**
 * Performance Analytics Panel Component
 * Metrics displaying strategy performance, signal history, and success rates with charts
 */

import React, { useState, useEffect, useCallback } from 'react'
import { signalEngineService } from '../../services/signalEngineService'
import type {
  SignalEnginePerformanceMetrics,
  GeneratedSignal,
  Strategy
} from '../../../../shared/types/signals'

/**
 * Performance analytics component props
 */
interface PerformanceAnalyticsPanelProps {
  /** Custom CSS classes */
  className?: string
  /** Strategy to analyze (if not provided, shows overall performance) */
  strategyId?: string
  /** Time period for analysis */
  timePeriod?: 'hour' | 'day' | 'week' | 'month' | 'all'
  /** Refresh interval in milliseconds */
  refreshInterval?: number
}

/**
 * Performance data interface
 */
interface PerformanceData {
  metrics: SignalEnginePerformanceMetrics | null
  signalHistory: GeneratedSignal[]
  strategies: Strategy[]
  timeSeriesData: TimeSeriesPoint[]
  enhancedAnalytics: EnhancedAnalytics | null
  isLoading: boolean
  error: string | null
}

/**
 * Time series data point
 */
interface TimeSeriesPoint {
  timestamp: number
  signalCount: number
  avgConfidence: number
  successRate: number
  buySignals: number
  sellSignals: number
  holdSignals: number
}

/**
 * Enhanced analytics data
 */
interface EnhancedAnalytics {
  signalDistribution: { buy: number; sell: number; hold: number }
  confidenceDistribution: { high: number; medium: number; low: number }
  strategyPerformance: Array<{
    strategy: string
    signals: number
    avgConfidence: number
    successRate: number
  }>
  hourlyActivity: Array<{
    hour: number
    signalCount: number
  }>
  trendAnalysis: {
    signalTrend: 'increasing' | 'decreasing' | 'stable'
    confidenceTrend: 'improving' | 'declining' | 'stable'
    trendStrength: number
  }
}

/**
 * Performance Analytics Panel Component
 */
export const PerformanceAnalyticsPanel: React.FC<PerformanceAnalyticsPanelProps> = ({
  className = '',
  strategyId,
  timePeriod = 'day',
  refreshInterval = 10000
}) => {
  // State management
  const [performanceData, setPerformanceData] = useState<PerformanceData>({
    metrics: null,
    signalHistory: [],
    strategies: [],
    timeSeriesData: [],
    enhancedAnalytics: null,
    isLoading: true,
    error: null
  })

  const [selectedMetric, setSelectedMetric] = useState<'signals' | 'confidence' | 'success'>('signals')
  const [selectedChart, setSelectedChart] = useState<'overview' | 'distribution' | 'trends' | 'strategies'>('overview')

  /**
   * Get time period filter
   */
  const getTimePeriodMs = useCallback((): number => {
    const now = Date.now()
    switch (timePeriod) {
      case 'hour':
        return now - (60 * 60 * 1000)
      case 'day':
        return now - (24 * 60 * 60 * 1000)
      case 'week':
        return now - (7 * 24 * 60 * 60 * 1000)
      case 'month':
        return now - (30 * 24 * 60 * 60 * 1000)
      default:
        return 0
    }
  }, [timePeriod])

  /**
   * Load performance data
   */
  const loadPerformanceData = useCallback(async () => {
    try {
      setPerformanceData(prev => ({ ...prev, isLoading: true, error: null }))

      // Get performance metrics via IPC
      const metrics = await signalEngineService.getPerformanceMetrics()

      // Get signal history via IPC
      const allSignals = await signalEngineService.getRecentSignals(1000) // Get more signals for analysis
      const timeFilter = getTimePeriodMs()
      const filteredSignals = timeFilter > 0
        ? allSignals.filter(signal => signal.timestamp >= timeFilter)
        : allSignals

      // Filter by strategy if specified
      const signalHistory = strategyId
        ? filteredSignals.filter(signal => signal.strategy === strategyId)
        : filteredSignals

      // Get active strategies via IPC
      const strategies = await signalEngineService.getActiveStrategies()

      // Generate time series data
      const timeSeriesData = generateTimeSeriesData(signalHistory, timePeriod)

      // Calculate enhanced analytics
      const enhancedAnalytics = calculateEnhancedAnalytics(signalHistory, strategies)

      setPerformanceData({
        metrics,
        signalHistory,
        strategies,
        timeSeriesData,
        enhancedAnalytics,
        isLoading: false,
        error: null
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load performance data'
      setPerformanceData(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
    }
  }, [strategyId, timePeriod, getTimePeriodMs])

  /**
   * Generate time series data for charts
   */
  const generateTimeSeriesData = (signals: GeneratedSignal[], period: string): TimeSeriesPoint[] => {
    if (signals.length === 0) return []

    const points: TimeSeriesPoint[] = []
    const now = Date.now()
    let intervalMs: number
    let pointCount: number

    // Determine interval and point count based on period
    switch (period) {
      case 'hour':
        intervalMs = 5 * 60 * 1000 // 5 minutes
        pointCount = 12
        break
      case 'day':
        intervalMs = 60 * 60 * 1000 // 1 hour
        pointCount = 24
        break
      case 'week':
        intervalMs = 24 * 60 * 60 * 1000 // 1 day
        pointCount = 7
        break
      case 'month':
        intervalMs = 24 * 60 * 60 * 1000 // 1 day
        pointCount = 30
        break
      default:
        intervalMs = 24 * 60 * 60 * 1000 // 1 day
        pointCount = 30
    }

    // Generate data points
    for (let i = pointCount - 1; i >= 0; i--) {
      const timestamp = now - (i * intervalMs)
      const endTime = timestamp + intervalMs
      
      const periodSignals = signals.filter(s => 
        s.timestamp >= timestamp && s.timestamp < endTime
      )

      const signalCount = periodSignals.length
      const avgConfidence = periodSignals.length > 0
        ? periodSignals.reduce((sum, s) => sum + s.confidence, 0) / periodSignals.length
        : 0

      // Calculate success rate (simplified - in real implementation, you'd need actual trade outcomes)
      const successRate = periodSignals.length > 0
        ? periodSignals.filter(s => s.confidence > 0.6).length / periodSignals.length
        : 0

      const buySignals = periodSignals.filter(s => s.signal === 'BUY').length
      const sellSignals = periodSignals.filter(s => s.signal === 'SELL').length
      const holdSignals = periodSignals.filter(s => s.signal === 'HOLD').length

      points.push({
        timestamp,
        signalCount,
        avgConfidence,
        successRate,
        buySignals,
        sellSignals,
        holdSignals
      })
    }

    return points
  }

  /**
   * Calculate signal distribution
   */
  const getSignalDistribution = useCallback(() => {
    const { signalHistory } = performanceData
    const total = signalHistory.length
    
    if (total === 0) {
      return { buy: 0, sell: 0, hold: 0 }
    }

    const buy = signalHistory.filter(s => s.signal === 'BUY').length
    const sell = signalHistory.filter(s => s.signal === 'SELL').length
    const hold = signalHistory.filter(s => s.signal === 'HOLD').length

    return {
      buy: (buy / total) * 100,
      sell: (sell / total) * 100,
      hold: (hold / total) * 100
    }
  }, [performanceData.signalHistory])

  /**
   * Calculate enhanced analytics
   */
  const calculateEnhancedAnalytics = useCallback((signals: GeneratedSignal[], strategies: Strategy[]): EnhancedAnalytics => {
    // Signal distribution
    const total = signals.length
    const signalDistribution = {
      buy: total > 0 ? (signals.filter(s => s.signal === 'BUY').length / total) * 100 : 0,
      sell: total > 0 ? (signals.filter(s => s.signal === 'SELL').length / total) * 100 : 0,
      hold: total > 0 ? (signals.filter(s => s.signal === 'HOLD').length / total) * 100 : 0
    }

    // Confidence distribution
    const confidenceDistribution = {
      high: total > 0 ? (signals.filter(s => s.confidence >= 0.7).length / total) * 100 : 0,
      medium: total > 0 ? (signals.filter(s => s.confidence >= 0.4 && s.confidence < 0.7).length / total) * 100 : 0,
      low: total > 0 ? (signals.filter(s => s.confidence < 0.4).length / total) * 100 : 0
    }

    // Strategy performance
    const strategyPerformance = strategies.map(strategy => {
      const strategySignals = signals.filter(s => s.strategy === strategy.name)
      const avgConfidence = strategySignals.length > 0
        ? strategySignals.reduce((sum, s) => sum + s.confidence, 0) / strategySignals.length
        : 0
      const successRate = strategySignals.length > 0
        ? (strategySignals.filter(s => s.confidence > 0.6).length / strategySignals.length) * 100
        : 0

      return {
        strategy: strategy.name,
        signals: strategySignals.length,
        avgConfidence,
        successRate
      }
    })

    // Hourly activity
    const hourlyActivity = Array.from({ length: 24 }, (_, hour) => {
      const hourSignals = signals.filter(s => {
        const signalHour = new Date(s.timestamp).getHours()
        return signalHour === hour
      })
      return {
        hour,
        signalCount: hourSignals.length
      }
    })

    // Trend analysis
    const recentSignals = signals.slice(-50) // Last 50 signals
    const olderSignals = signals.slice(-100, -50) // Previous 50 signals

    const recentAvgConfidence = recentSignals.length > 0
      ? recentSignals.reduce((sum, s) => sum + s.confidence, 0) / recentSignals.length
      : 0
    const olderAvgConfidence = olderSignals.length > 0
      ? olderSignals.reduce((sum, s) => sum + s.confidence, 0) / olderSignals.length
      : 0

    const confidenceDiff = recentAvgConfidence - olderAvgConfidence
    const signalCountDiff = recentSignals.length - olderSignals.length

    const trendAnalysis = {
      signalTrend: signalCountDiff > 5 ? 'increasing' as const :
                   signalCountDiff < -5 ? 'decreasing' as const : 'stable' as const,
      confidenceTrend: confidenceDiff > 0.1 ? 'improving' as const :
                       confidenceDiff < -0.1 ? 'declining' as const : 'stable' as const,
      trendStrength: Math.abs(confidenceDiff) + Math.abs(signalCountDiff / 10)
    }

    return {
      signalDistribution,
      confidenceDistribution,
      strategyPerformance,
      hourlyActivity,
      trendAnalysis
    }
  }, [])

  /**
   * Calculate confidence distribution
   */
  const getConfidenceDistribution = useCallback(() => {
    const { signalHistory } = performanceData
    const total = signalHistory.length
    
    if (total === 0) {
      return { high: 0, medium: 0, low: 0 }
    }

    const high = signalHistory.filter(s => s.confidence >= 0.7).length
    const medium = signalHistory.filter(s => s.confidence >= 0.4 && s.confidence < 0.7).length
    const low = signalHistory.filter(s => s.confidence < 0.4).length

    return {
      high: (high / total) * 100,
      medium: (medium / total) * 100,
      low: (low / total) * 100
    }
  }, [performanceData.signalHistory])

  /**
   * Get strategy performance summary
   */
  const getStrategyPerformance = useCallback(() => {
    const { signalHistory } = performanceData
    const strategyStats = new Map<string, {
      count: number
      avgConfidence: number
      lastSignal: number
    }>()

    signalHistory.forEach(signal => {
      const existing = strategyStats.get(signal.strategy) || {
        count: 0,
        avgConfidence: 0,
        lastSignal: 0
      }

      existing.count++
      existing.avgConfidence = (existing.avgConfidence * (existing.count - 1) + signal.confidence) / existing.count
      existing.lastSignal = Math.max(existing.lastSignal, signal.timestamp)

      strategyStats.set(signal.strategy, existing)
    })

    return Array.from(strategyStats.entries()).map(([name, stats]) => ({
      name,
      ...stats
    })).sort((a, b) => b.count - a.count)
  }, [performanceData.signalHistory])

  // Load data on mount and set up refresh interval
  useEffect(() => {
    loadPerformanceData()

    const interval = setInterval(loadPerformanceData, refreshInterval)
    return () => clearInterval(interval)
  }, [loadPerformanceData, refreshInterval])

  /**
   * Format timestamp for display
   */
  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp)
    switch (timePeriod) {
      case 'hour':
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      case 'day':
        return date.toLocaleTimeString([], { hour: '2-digit' })
      case 'week':
      case 'month':
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
      default:
        return date.toLocaleDateString()
    }
  }

  /**
   * Render loading state
   */
  if (performanceData.isLoading && !performanceData.metrics) {
    return (
      <div className={`performance-analytics-panel ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          <span className="ml-3 text-gray-600">Loading Performance Analytics...</span>
        </div>
      </div>
    )
  }

  /**
   * Render error state
   */
  if (performanceData.error) {
    return (
      <div className={`performance-analytics-panel ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <span className="text-red-600 text-xl mr-2">⚠️</span>
            <div>
              <h3 className="text-red-800 font-medium">Analytics Error</h3>
              <p className="text-red-600 text-sm mt-1">{performanceData.error}</p>
            </div>
          </div>
          <button
            onClick={loadPerformanceData}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const signalDistribution = getSignalDistribution()
  const confidenceDistribution = getConfidenceDistribution()
  const strategyPerformance = getStrategyPerformance()

  return (
    <div className={`performance-analytics-panel bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <span className="mr-2">📊</span>
            Performance Analytics
            {strategyId && (
              <span className="ml-2 text-sm font-normal text-gray-500">
                • {strategyId}
              </span>
            )}
          </h3>
          <div className="flex items-center space-x-4">
            {/* Chart Selection */}
            <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              {[
                { key: 'overview', label: '📈 Overview', icon: '📈' },
                { key: 'distribution', label: '🥧 Distribution', icon: '🥧' },
                { key: 'trends', label: '📊 Trends', icon: '📊' },
                { key: 'strategies', label: '⚙️ Strategies', icon: '⚙️' }
              ].map(({ key, label, icon }) => (
                <button
                  key={key}
                  onClick={() => setSelectedChart(key as any)}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    selectedChart === key
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {icon} {label.split(' ')[1]}
                </button>
              ))}
            </div>

            {/* Time Period Selection */}
            <select
              value={timePeriod}
              onChange={(e) => window.location.reload()} // Simplified - in real app, would update props
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="hour">Last Hour</option>
              <option value="day">Last Day</option>
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Key Metrics */}
        {performanceData.metrics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Total Signals</p>
                  <p className="text-2xl font-bold text-blue-900">
                    {performanceData.signalHistory.length}
                  </p>
                </div>
                <div className="text-2xl">🎯</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Avg Confidence</p>
                  <p className="text-2xl font-bold text-green-900">
                    {performanceData.signalHistory.length > 0
                      ? (performanceData.signalHistory.reduce((sum, s) => sum + s.confidence, 0) / performanceData.signalHistory.length * 100).toFixed(1)
                      : '0.0'
                    }%
                  </p>
                </div>
                <div className="text-2xl">📈</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Active Strategies</p>
                  <p className="text-2xl font-bold text-purple-900">
                    {performanceData.strategies.length}
                  </p>
                </div>
                <div className="text-2xl">⚙️</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">Avg Processing</p>
                  <p className="text-2xl font-bold text-orange-900">
                    {performanceData.metrics.averageProcessingTime.toFixed(1)}ms
                  </p>
                </div>
                <div className="text-2xl">⚡</div>
              </div>
            </div>
          </div>
        )}

        {/* Dynamic Chart Content */}
        {selectedChart === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Time Series Chart */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-4">📈 Signal Activity Over Time</h4>
              <div className="h-48 flex items-end space-x-2">
                {performanceData.timeSeriesData.slice(-12).map((point, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-green-500 rounded-t transition-all duration-300"
                      style={{
                        height: `${Math.max(4, (point.signalCount / Math.max(...performanceData.timeSeriesData.map(p => p.signalCount))) * 160)}px`
                      }}
                      title={`${point.signalCount} signals at ${formatTimestamp(point.timestamp)}`}
                    />
                    <span className="text-xs text-gray-500 mt-1 transform -rotate-45 origin-left">
                      {formatTimestamp(point.timestamp)}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Confidence Trend */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-4">🎯 Confidence Trend</h4>
              <div className="h-48 flex items-end space-x-2">
                {performanceData.timeSeriesData.slice(-12).map((point, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-blue-500 rounded-t transition-all duration-300"
                      style={{
                        height: `${Math.max(4, point.avgConfidence * 160)}px`
                      }}
                      title={`${(point.avgConfidence * 100).toFixed(1)}% avg confidence`}
                    />
                    <span className="text-xs text-gray-500 mt-1">
                      {(point.avgConfidence * 100).toFixed(0)}%
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {selectedChart === 'distribution' && performanceData.enhancedAnalytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Enhanced Signal Distribution */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-4">🥧 Signal Type Distribution</h4>
              <div className="space-y-3">
                {[
                  { type: 'BUY', value: performanceData.enhancedAnalytics.signalDistribution.buy, color: 'bg-green-500', icon: '📈' },
                  { type: 'SELL', value: performanceData.enhancedAnalytics.signalDistribution.sell, color: 'bg-red-500', icon: '📉' },
                  { type: 'HOLD', value: performanceData.enhancedAnalytics.signalDistribution.hold, color: 'bg-yellow-500', icon: '⏸️' }
                ].map(({ type, value, color, icon }) => (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">{icon}</span>
                      <div className={`w-3 h-3 ${color} rounded`}></div>
                      <span className="text-sm text-gray-600">{type} Signals</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className={`${color} h-2 rounded-full transition-all duration-500`}
                          style={{ width: `${value}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-12">
                        {value.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Confidence Distribution */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-4">🎯 Confidence Distribution</h4>
              <div className="space-y-3">
                {[
                  { level: 'High (70%+)', value: performanceData.enhancedAnalytics.confidenceDistribution.high, color: 'bg-green-500', icon: '🟢' },
                  { level: 'Medium (40-70%)', value: performanceData.enhancedAnalytics.confidenceDistribution.medium, color: 'bg-yellow-500', icon: '🟡' },
                  { level: 'Low (<40%)', value: performanceData.enhancedAnalytics.confidenceDistribution.low, color: 'bg-red-500', icon: '🔴' }
                ].map(({ level, value, color, icon }) => (
                  <div key={level} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">{icon}</span>
                      <span className="text-sm text-gray-600">{level}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className={`${color} h-2 rounded-full transition-all duration-500`}
                          style={{ width: `${value}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-12">
                        {value.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {selectedChart === 'trends' && performanceData.enhancedAnalytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Trend Analysis */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-4">📊 Trend Analysis</h4>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-white rounded border">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">📈</span>
                    <span className="text-sm font-medium">Signal Trend</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${
                      performanceData.enhancedAnalytics.trendAnalysis.signalTrend === 'increasing' ? 'text-green-600' :
                      (performanceData.enhancedAnalytics.trendAnalysis.signalTrend === 'decreasing' ? 'text-red-600' : 'text-gray-600')
                    }`}>
                      {performanceData.enhancedAnalytics.trendAnalysis.signalTrend === 'increasing' ? '↗️ Increasing' :
                       performanceData.enhancedAnalytics.trendAnalysis.signalTrend === 'decreasing' ? '↘️ Decreasing' : '➡️ Stable'}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-white rounded border">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">🎯</span>
                    <span className="text-sm font-medium">Confidence Trend</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${
                      performanceData.enhancedAnalytics.trendAnalysis.confidenceTrend === 'improving' ? 'text-green-600' :
                      (performanceData.enhancedAnalytics.trendAnalysis.confidenceTrend === 'declining' ? 'text-red-600' : 'text-gray-600')
                    }`}>
                      {performanceData.enhancedAnalytics.trendAnalysis.confidenceTrend === 'improving' ? '📈 Improving' :
                       performanceData.enhancedAnalytics.trendAnalysis.confidenceTrend === 'declining' ? '📉 Declining' : '➡️ Stable'}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-white rounded border">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">⚡</span>
                    <span className="text-sm font-medium">Trend Strength</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${Math.min(100, performanceData.enhancedAnalytics.trendAnalysis.trendStrength * 20)}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {(performanceData.enhancedAnalytics.trendAnalysis.trendStrength * 20).toFixed(0)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Hourly Activity */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-4">🕐 Hourly Activity Pattern</h4>
              <div className="h-32 flex items-end space-x-1">
                {performanceData.enhancedAnalytics.hourlyActivity.map((activity, index) => (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-purple-500 rounded-t transition-all duration-300"
                      style={{
                        height: `${Math.max(2, (activity.signalCount / Math.max(...performanceData.enhancedAnalytics!.hourlyActivity.map(a => a.signalCount))) * 100)}px`
                      }}
                      title={`${activity.signalCount} signals at ${activity.hour}:00`}
                    />
                    {index % 4 === 0 && (
                      <span className="text-xs text-gray-500 mt-1">
                        {activity.hour}h
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {selectedChart === 'strategies' && performanceData.enhancedAnalytics && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">⚙️ Strategy Performance Comparison</h4>
            <div className="grid grid-cols-1 gap-4">
              {performanceData.enhancedAnalytics.strategyPerformance.map((strategy, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-gray-900">{strategy.strategy}</h5>
                    <span className="text-sm text-gray-500">{strategy.signals} signals</span>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-600">Avg Confidence</span>
                        <span className="text-sm font-medium">{(strategy.avgConfidence * 100).toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${strategy.avgConfidence * 100}%` }}
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-600">Success Rate</span>
                        <span className="text-sm font-medium">{strategy.successRate.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${strategy.successRate}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Fallback for old charts */}
        {selectedChart === 'overview' && !performanceData.enhancedAnalytics && (
          <>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Signal Distribution */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4">Signal Distribution</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span className="text-sm text-gray-600">BUY Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${signalDistribution.buy}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">
                    {signalDistribution.buy.toFixed(1)}%
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span className="text-sm text-gray-600">SELL Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-red-500 h-2 rounded-full"
                      style={{ width: `${signalDistribution.sell}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">
                    {signalDistribution.sell.toFixed(1)}%
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                  <span className="text-sm text-gray-600">HOLD Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-yellow-500 h-2 rounded-full"
                      style={{ width: `${signalDistribution.hold}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-12">
                    {signalDistribution.hold.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Strategy Performance */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4">Top Strategies</h4>
            {strategyPerformance.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-4xl mb-2">📊</div>
                <p className="text-gray-500">No strategy data available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {strategyPerformance.slice(0, 5).map((strategy, index) => {
                  const confidenceColor = strategy.avgConfidence >= 0.7 ? 'text-green-600' :
                    (strategy.avgConfidence >= 0.5 ? 'text-yellow-600' : 'text-red-600')

                  return (
                    <div key={strategy.name} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{strategy.name}</div>
                        <div className="text-sm text-gray-500">{strategy.count} signals</div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${confidenceColor}`}>
                          {(strategy.avgConfidence * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-500">confidence</div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-4">Confidence Distribution</h4>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {confidenceDistribution.high.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">High (&ge;70%)</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-green-500 h-2 rounded-full"
                  style={{ width: `${confidenceDistribution.high}%` }}
                ></div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {confidenceDistribution.medium.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Medium (40-70%)</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-yellow-500 h-2 rounded-full"
                  style={{ width: `${confidenceDistribution.medium}%` }}
                ></div>
              </div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {confidenceDistribution.low.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Low (&lt;40%)</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className="bg-red-500 h-2 rounded-full"
                  style={{ width: `${confidenceDistribution.low}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
        )}
      </div>
    </div>
  )
}

export default PerformanceAnalyticsPanel
